import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types (based on our schema)
export interface Profile {
  id: string
  name: string | null
  email: string | null
  avatar_url: string | null
  created_at: string
  updated_at: string
}

export interface UserPreferences {
  id: string
  user_id: string
  theme: 'light' | 'dark' | 'system'
  language: string
  settings: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ChatSession {
  id: string
  user_id: string
  title: string
  icon_name: string
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  chat_session_id: string
  content: string
  sender_type: 'user' | 'ai'
  timestamp: string
  metadata: Record<string, any>
  is_loading: boolean
  is_error: boolean
  avatar_url: string | null
  grounding_chunks: any[]
}

// Helper functions for common operations
export const supabaseHelpers = {
  // Auth helpers
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  // Profile helpers
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  async updateProfile(userId: string, updates: Partial<Profile>) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  // Chat helpers
  async getChatSessions(userId: string) {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
    return { data, error }
  },

  async createChatSession(userId: string, title: string, iconName: string = 'SpeechBubbleIcon') {
    const { data, error } = await supabase
      .from('chat_sessions')
      .insert({
        user_id: userId,
        title,
        icon_name: iconName
      })
      .select()
      .single()
    return { data, error }
  },

  async updateChatSession(chatId: string, updates: Partial<ChatSession>) {
    const { data, error } = await supabase
      .from('chat_sessions')
      .update(updates)
      .eq('id', chatId)
      .select()
      .single()
    return { data, error }
  },

  async deleteChatSession(chatId: string) {
    const { error } = await supabase
      .from('chat_sessions')
      .delete()
      .eq('id', chatId)
    return { error }
  },

  // Message helpers
  async getMessages(chatSessionId: string) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_session_id', chatSessionId)
      .order('timestamp', { ascending: true })
    return { data, error }
  },

  async addMessage(message: Omit<Message, 'id' | 'timestamp'>) {
    console.log('🔍 [supabaseHelpers.addMessage] Inserting message:', {
      chat_session_id: message.chat_session_id,
      content: message.content.substring(0, 50),
      sender_type: message.sender_type
    });

    const { data, error } = await supabase
      .from('messages')
      .insert(message)
      .select()
      .single()

    if (error) {
      console.error('❌ [supabaseHelpers.addMessage] Database error:', error);
    } else {
      console.log('✅ [supabaseHelpers.addMessage] Message inserted successfully:', data?.id);
    }

    return { data, error }
  },

  async updateMessage(messageId: string, updates: Partial<Message>) {
    const { data, error } = await supabase
      .from('messages')
      .update(updates)
      .eq('id', messageId)
      .select()
      .single()
    return { data, error }
  },

  // Real-time subscriptions
  subscribeToChatMessages(chatSessionId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`messages:${chatSessionId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${chatSessionId}`
        },
        callback
      )
      .subscribe()
  },

  subscribeToChatSessions(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`chat_sessions:${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chat_sessions',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe()
  }
}
