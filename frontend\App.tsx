import React, { useState, useCallback, useEffect } from "react";
import Sidebar from "./components/Sidebar";
import MainArea from "./components/MainArea";
import SettingsPage from "./components/SettingsPage";
import SignInModal from "./components/SignInModal";
import ChatHistoryAccessModal from "./components/ChatHistoryAccessModal";
import AccountModal from "./components/AccountModal";
import { resetChatSession } from "./services/geminiService";
import { dataService } from "./services/dataService";
import {
  AuthView,
  ChatSession,
  User,
  Message,
  RawChatSession,
  UserProfiles,
  SettingsSubView as AppSettingsSubView,
} from "./types";
import { getCurrentUser } from "./constants";
import { useTranslation } from "react-i18next";

export type View = "chat" | "settings";
export type SettingsSubView = AppSettingsSubView;
export type Theme = "light" | "dark" | "system";

const App: React.FC = () => {
  const { t } = useTranslation();
  const [activeChatId, setActiveChatId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 768);
  const [currentView, setCurrentView] = useState<View>("chat");
  const [settingsSubView, setSettingsSubView] =
    useState<SettingsSubView>("main");

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [activeModal, setActiveModal] = useState<AuthView>(null);
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false);

  const [userProfiles, setUserProfiles] = useState<UserProfiles | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [dataLoading, setDataLoading] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);

  const [theme, setTheme] = useState<Theme>(() => {
    const storedTheme = localStorage.getItem("theme") as Theme | null;
    return storedTheme || "system";
  });

  useEffect(() => {
    // Helper function to apply the actual 'dark' or 'light' mode to the DOM
    const applyModeToDOM = (userPreference: Theme) => {
      let mode: "light" | "dark";
      if (userPreference === "system") {
        mode = window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light";
      } else {
        mode = userPreference;
      }

      if (mode === "dark") {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    };

    // Apply the theme when the component mounts or when the 'theme' state changes
    localStorage.setItem("theme", theme); // Save user's explicit preference
    applyModeToDOM(theme); // Apply to DOM based on this preference

    // Listener for system preference changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleSystemChange = () => {
      // Only re-apply if the user's current preference is 'system'
      if (theme === "system") {
        applyModeToDOM("system");
      }
    };

    mediaQuery.addEventListener("change", handleSystemChange);

    // Cleanup listener on component unmount or before effect re-runs
    return () => mediaQuery.removeEventListener("change", handleSystemChange);
  }, [theme]); // Re-run this effect when the user's theme preference changes

  useEffect(() => {
    const fetchData = async () => {
      console.log('🔍 [App] Starting initial data fetch...');
      setDataLoading(true);
      setDataError(null);

      // Add a timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        console.error('❌ [App] Data fetch timeout after 10 seconds');
        setDataError('Data loading timed out. Please refresh the page.');
        setDataLoading(false);
      }, 10000);

      try {
        // Check authentication status
        console.log('🔍 [App] Checking authentication status...');
        const isAuth = await dataService.isAuthenticated();
        console.log('🔍 [App] Authentication status:', isAuth);
        setIsAuthenticated(isAuth);

        // Fetch user profiles and chat sessions
        console.log('🔍 [App] Fetching user profiles and chat sessions...');
        const [userProfilesData, chatSessionsData] = await Promise.all([
          dataService.getUserProfiles(),
          dataService.getChatSessions(),
        ]);

        console.log('🔍 [App] User profiles data:', !!userProfilesData);
        console.log('🔍 [App] Chat sessions data:', chatSessionsData?.length || 0);

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
        }

        setChatSessions(chatSessionsData);
        console.log('✅ [App] Initial data fetch completed successfully');
        clearTimeout(timeoutId);
      } catch (error) {
        console.error("❌ [App] Error fetching initial data:", error);
        setDataError(
          error instanceof Error ? error.message : "An unknown error occurred",
        );
        clearTimeout(timeoutId);
      } finally {
        console.log('🔍 [App] Setting dataLoading to false');
        setDataLoading(false);
      }
    };

    fetchData();

    // Listen for auth state changes
    const { data: { subscription } } = dataService.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      setIsAuthenticated(!!session);

      if (session) {
        // User signed in, refresh data
        const [userProfilesData, chatSessionsData] = await Promise.all([
          dataService.getUserProfiles(),
          dataService.getChatSessions(),
        ]);

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
        }
        setChatSessions(chatSessionsData);
      } else {
        // User signed out, clear data or load fallback
        const [userProfilesData, chatSessionsData] = await Promise.all([
          dataService.getUserProfiles(),
          dataService.getChatSessions(),
        ]);

        if (userProfilesData) {
          setUserProfiles(userProfilesData);
        }
        setChatSessions(chatSessionsData);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const smallScreen = window.innerWidth < 768;
      setIsSmallScreen(smallScreen);
      if (isAuthenticated) {
        if (window.innerWidth >= 768) {
          setIsSidebarOpen(true);
        } else {
          setIsSidebarOpen(false);
        }
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, [isAuthenticated]);

  useEffect(() => {
    if (!isAuthenticated) {
      setIsSidebarOpen(false);
    } else {
      setIsSidebarOpen(window.innerWidth >= 768);
    }
  }, [isAuthenticated]);

  const handleLogin = useCallback(async () => {
    // Authentication state will be handled by the auth state change listener
    setActiveModal(null);
    setCurrentView("chat");
    setSettingsSubView("main");
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      await dataService.signOut();
      // Authentication state will be handled by the auth state change listener
      setActiveChatId(null);
      resetChatSession();
      setCurrentView("chat");
      setActiveModal(null);
      setIsAccountModalOpen(false);
      setSettingsSubView("main");
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }, []);

  const openSignInModal = useCallback(() => {
    setActiveModal("signIn");
  }, []);

  const openChatHistoryAccessModal = useCallback(() => {
    setActiveModal("chatHistoryAccess");
  }, []);

  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  const sidebarToggleHandler = useCallback(() => {
    if (!isAuthenticated) {
      openChatHistoryAccessModal();
    } else {
      setIsSidebarOpen((prev) => !prev);
    }
  }, [isAuthenticated, openChatHistoryAccessModal]);

  const navigateTo = useCallback(
    (view: View, subView: SettingsSubView = "main") => {
      setCurrentView(view);
      setSettingsSubView(subView);
      if (isAuthenticated && isSmallScreen) {
        if (view === "settings" || view === "chat") {
          setIsSidebarOpen(false);
        }
      }
    },
    [isSmallScreen, isAuthenticated],
  );

  const handleNewChat = useCallback(() => {
    setActiveChatId(null);
    resetChatSession();
    setCurrentView("chat");
    setSettingsSubView("main");
    if (isAuthenticated && isSmallScreen) {
      setIsSidebarOpen(false);
    }
  }, [isSmallScreen, isAuthenticated]);

  const handleSelectChat = useCallback(
    async (chatId: string) => {
      if (!isAuthenticated) {
        openChatHistoryAccessModal();
        return;
      }
      resetChatSession();
      setActiveChatId(chatId);
      setCurrentView("chat");
      setSettingsSubView("main");
      if (isSmallScreen) {
        setIsSidebarOpen(false);
      }

      // Load messages for the selected chat if they haven't been loaded yet
      const selectedSession = chatSessions.find(s => s.id === chatId);
      if (selectedSession && selectedSession.messages.length === 0) {
        try {
          console.log('🔍 [handleSelectChat] Loading messages for chat:', chatId);
          const messages = await dataService.getMessagesForSession(chatId);
          setChatSessions(prevSessions =>
            prevSessions.map(session =>
              session.id === chatId
                ? { ...session, messages }
                : session
            )
          );
          console.log('✅ [handleSelectChat] Messages loaded for chat:', chatId);
        } catch (error) {
          console.error('❌ [handleSelectChat] Error loading messages:', error);
        }
      }
    },
    [isSmallScreen, isAuthenticated, openChatHistoryAccessModal, chatSessions],
  );

  const updateChatSession = useCallback(
    (
      chatId: string,
      messages: Message[],
      details?: { isNew?: boolean; title?: string },
    ) => {
      const now = new Date();

      // Save to Supabase in background (non-blocking)
      (async () => {
        try {
          // Check if user is authenticated
          const isAuth = await dataService.isAuthenticated();
          console.log('🔍 [updateChatSession] Authentication check:', isAuth);
          console.log('🔍 [updateChatSession] Chat ID:', chatId, 'Messages count:', messages.length);

          if (isAuth) {
            // Save to Supabase
            if (details?.isNew) {
              console.log('🔍 [updateChatSession] Creating new chat session...');
              // Create new chat session in Supabase
              const newSession = await dataService.createChatSession(
                details.title || `Chat ${now.toLocaleTimeString()}`,
                "SpeechBubbleIcon"
              );
              console.log('🔍 [updateChatSession] New session created:', newSession);

              if (newSession) {
                // Update the chatId to use the Supabase-generated UUID
                const supabaseChatId = newSession.id;

                // Save all messages to Supabase
                console.log('🔍 [updateChatSession] Saving messages to Supabase:', messages.length);
                for (const message of messages) {
                  try {
                    console.log('🔍 [updateChatSession] Saving message:', message.id, message.text.substring(0, 50));
                    const savedMessage = await dataService.addMessage(supabaseChatId, {
                      ...message,
                      chatId: supabaseChatId
                    });
                    console.log('🔍 [updateChatSession] Message saved successfully:', savedMessage?.id);
                  } catch (msgError) {
                    console.error('❌ Error saving message:', msgError);
                  }
                }

                // Update local state with Supabase chat ID
                setChatSessions((prevSessions) => {
                  const updatedSession: ChatSession = {
                    ...newSession,
                    messages: messages.map(msg => ({ ...msg, chatId: supabaseChatId }))
                  };
                  return [updatedSession, ...prevSessions.filter((s) => s.id !== chatId && s.id !== supabaseChatId)];
                });

                // Update active chat ID to the Supabase ID
                setActiveChatId(supabaseChatId);
                return;
              }
            } else {
              // Update existing chat session
              console.log('🔍 [updateChatSession] Updating existing chat session:', chatId);
              // Find new messages that need to be saved
              const existingSession = chatSessions.find(s => s.id === chatId);
              const existingMessageIds = new Set(existingSession?.messages.map(m => m.id) || []);
              const newMessages = messages.filter(msg => !existingMessageIds.has(msg.id));
              console.log('🔍 [updateChatSession] New messages to save:', newMessages.length);

              // Save new messages to Supabase
              for (const message of newMessages) {
                try {
                  console.log('🔍 [updateChatSession] Saving new message to existing chat:', message.id);
                  const savedMessage = await dataService.addMessage(chatId, message);
                  console.log('🔍 [updateChatSession] Message saved to existing chat:', savedMessage?.id);
                } catch (msgError) {
                  console.error('❌ Error saving message to existing chat:', msgError);
                }
              }
            }
          } else {
            console.log('🔍 [updateChatSession] User not authenticated, skipping Supabase save');
          }
        } catch (error) {
          console.error('❌ Error in updateChatSession:', error);
          // Continue with local state update even if Supabase fails
        }
      })();

      // Update local state (fallback or for non-authenticated users)
      setChatSessions((prevSessions) => {
        if (details?.isNew) {
          const newSession: ChatSession = {
            id: chatId,
            title: details.title || `Chat ${now.toLocaleTimeString()}`,
            messages: messages,
            lastActivity: now,
            iconName: "SpeechBubbleIcon",
          };
          return [newSession, ...prevSessions.filter((s) => s.id !== chatId)];
        }
        return prevSessions
          .map((session) =>
            session.id === chatId
              ? { ...session, messages: messages, lastActivity: now }
              : session,
          )
          .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
      });
    },
    [chatSessions, setActiveChatId],
  );

  const openAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "account");
    } else {
      setIsAccountModalOpen(true);
    }
  };

  const closeAccountModalHandler = () => {
    if (isSmallScreen) {
      navigateTo("settings", "main");
    } else {
      setIsAccountModalOpen(false);
    }
  };

  const handleSaveAccountChanges = (updatedUserData: Partial<User>) => {
    if (userProfiles) {
      setUserProfiles((prevProfiles) => {
        if (!prevProfiles) return null;
        return {
          ...prevProfiles,
          loggedInUser: {
            ...prevProfiles.loggedInUser,
            ...updatedUserData,
          },
        };
      });
    }
    closeAccountModalHandler();
    console.log("Account changes saved (simulated):", updatedUserData);
  };

  const exportData = () => {
    const chatsToExport = chatSessions.map((session) => ({
      ...session,
      lastActivity: session.lastActivity.toISOString(),
      icon: undefined,
    }));
    const chatsJsonString = JSON.stringify(chatsToExport, null, 2);
    const chatsBlob = new Blob([chatsJsonString], { type: "application/json" });
    const chatsUrl = URL.createObjectURL(chatsBlob);
    const chatsLink = document.createElement("a");
    chatsLink.href = chatsUrl;
    chatsLink.download = "chats_updated.json";
    document.body.appendChild(chatsLink);
    chatsLink.click();
    document.body.removeChild(chatsLink);
    URL.revokeObjectURL(chatsUrl);

    if (userProfiles) {
      console.log(
        "Exporting userProfiles state:",
        JSON.parse(JSON.stringify(userProfiles)),
      );
      const usersJsonString = JSON.stringify(userProfiles, null, 2);
      const usersBlob = new Blob([usersJsonString], {
        type: "application/json",
      });
      const usersUrl = URL.createObjectURL(usersBlob);
      const usersLink = document.createElement("a");
      usersLink.href = usersUrl;
      usersLink.download = "users_updated.json";
      document.body.appendChild(usersLink);
      usersLink.click();
      document.body.removeChild(usersLink);
      URL.revokeObjectURL(usersUrl);
    }
  };

  const currentUser = getCurrentUser(isAuthenticated, userProfiles);

  if (dataLoading) {
    return (
      <div
        className="flex items-center justify-center h-screen text-slate-700 dark:text-slate-300 text-lg bg-white dark:bg-slate-900"
        data-oid="sh1r8me"
      >
        Loading application data...
      </div>
    );
  }

  if (dataError) {
    return (
      <div
        className="flex flex-col items-center justify-center h-screen text-red-600 dark:text-red-400 p-4 bg-white dark:bg-slate-900"
        data-oid="oz-qi0r"
      >
        <h1 className="text-xl font-semibold mb-2" data-oid="3k33w3o">
          Error Loading Data
        </h1>
        <p className="text-center" data-oid="13.r9tx">
          {dataError}
        </p>
        <p
          className="mt-4 text-sm text-slate-500 dark:text-slate-400"
          data-oid="tpaoadn"
        >
          Please try refreshing the page or check the console for more details.
        </p>
      </div>
    );
  }

  const accountModalToShow =
    (currentView === "settings" &&
      settingsSubView === "account" &&
      isSmallScreen) ||
    (isAccountModalOpen && !isSmallScreen);

  return (
    <div
      className="flex h-screen overflow-hidden bg-slate-100 dark:bg-slate-900"
      data-oid="nj:ly4f"
    >
      {isAuthenticated &&
        isSmallScreen &&
        isSidebarOpen &&
        currentView === "chat" && (
          <div
            className="fixed inset-0 bg-black/50 dark:bg-black/70 z-40 transition-opacity duration-300 ease-in-out"
            onClick={() => setIsSidebarOpen(false)}
            aria-hidden="true"
            data-oid="q9u.v8q"
          />
        )}
      <Sidebar
        chatSessions={chatSessions}
        onNewChat={handleNewChat}
        onSelectChat={handleSelectChat}
        activeChatId={activeChatId}
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={sidebarToggleHandler}
        isSmallScreen={isSmallScreen}
        navigateTo={navigateTo}
        currentView={currentView}
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        handleLogout={handleLogout}
        openSignInModal={openSignInModal}
        data-oid="7-c5ox5"
      />

      {currentView === "chat" ? (
        <MainArea
          activeChatId={activeChatId}
          setActiveChatId={setActiveChatId}
          chatMessages={
            chatSessions.find((cs) => cs.id === activeChatId)?.messages || []
          }
          onUpdateChat={updateChatSession}
          isSidebarOpen={isSidebarOpen}
          onToggleSidebar={sidebarToggleHandler}
          onNewChat={handleNewChat}
          navigateTo={navigateTo}
          isAuthenticated={isAuthenticated}
          currentUser={currentUser}
          handleLogout={handleLogout}
          openSignInModal={openSignInModal}
          data-oid="8z2z0f3"
        />
      ) : currentView === "settings" && settingsSubView === "main" ? (
        <SettingsPage
          navigateTo={navigateTo}
          isSmallScreen={isSmallScreen}
          openAccountModal={openAccountModalHandler}
          settingsSubView={settingsSubView}
          currentTheme={theme}
          onThemeChange={setTheme}
          data-oid="jy8:kpe"
        />
      ) : null}

      {accountModalToShow && (
        <AccountModal
          isOpen={true}
          onClose={closeAccountModalHandler}
          onSave={handleSaveAccountChanges}
          currentUser={currentUser}
          isSmallScreen={isSmallScreen}
          data-oid="l8n6jc2"
        />
      )}

      {activeModal === "signIn" && (
        <SignInModal
          isOpen={true}
          onClose={closeModal}
          onSignIn={handleLogin}
          data-oid="8gszv0w"
        />
      )}
      {activeModal === "chatHistoryAccess" && (
        <ChatHistoryAccessModal
          isOpen={true}
          onClose={closeModal}
          onSignInRedirect={() => {
            closeModal();
            openSignInModal();
          }}
          data-oid="qx1_9wx"
        />
      )}
      <div className="fixed bottom-2 right-2 z-[100]" data-oid="t.4vuuo">
        <button
          onClick={exportData}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow"
          title={t(
            "exportDataTooltip",
            "Download chat sessions and user data as JSON",
          )}
          data-oid="yahgk.0"
        >
          {isAuthenticated
            ? t("exportData")
            : t("exportChatsFallback", "Export Chats")}
        </button>
      </div>
    </div>
  );
};

export default App;
