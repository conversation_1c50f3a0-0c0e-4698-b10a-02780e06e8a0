import { supabase, supabaseHelpers } from '../lib/supabase'
import { ChatSession, Message, User, UserProfiles } from '../types'

// Data service that handles both Supabase and fallback to JSON files
export class DataService {
  private static instance: DataService
  private isSupabaseAvailable = true

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService()
    }
    return DataService.instance
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      return !!user
    } catch (error) {
      console.error('Error checking authentication:', error)
      return false
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      const { data: profile } = await supabaseHelpers.getProfile(user.id)
      if (!profile) return null

      return {
        name: profile.name || user.email || '',
        email: profile.email || user.email || '',
        avatarUrl: profile.avatar_url || ''
      }
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  // Get user profiles (for compatibility with existing code)
  async getUserProfiles(): Promise<UserProfiles | null> {
    try {
      const currentUser = await this.getCurrentUser()
      if (!currentUser) {
        // Fallback to JSON data if not authenticated
        return this.getFallbackUserProfiles()
      }

      return {
        loggedInUser: currentUser,
        guestUser: {
          name: 'Guest',
          avatarUrl: '',
          email: ''
        }
      }
    } catch (error) {
      console.error('Error getting user profiles:', error)
      return this.getFallbackUserProfiles()
    }
  }

  // Get chat sessions
  async getChatSessions(): Promise<ChatSession[]> {
    try {
      console.log('🔍 [getChatSessions] Starting to fetch chat sessions...');
      const isAuth = await this.isAuthenticated()
      console.log('🔍 [getChatSessions] Authentication status:', isAuth);

      if (!isAuth) {
        console.log('🔍 [getChatSessions] User not authenticated, using fallback data');
        // Fallback to JSON data if not authenticated
        return this.getFallbackChatSessions()
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        console.log('🔍 [getChatSessions] No user found, returning empty array');
        return []
      }

      console.log('🔍 [getChatSessions] Fetching chat sessions for user:', user.id);
      const { data: chatSessions, error } = await supabaseHelpers.getChatSessions(user.id)
      if (error) {
        console.error('❌ [getChatSessions] Error fetching chat sessions:', error)
        return this.getFallbackChatSessions()
      }

      console.log('🔍 [getChatSessions] Found chat sessions:', chatSessions?.length || 0);

      // Convert Supabase format to app format (without loading messages initially for performance)
      console.log('🔍 [getChatSessions] Converting sessions to app format (without messages for performance)...');
      const convertedSessions: ChatSession[] = (chatSessions || []).map((session) => {
        return {
          id: session.id,
          title: session.title,
          messages: [], // Load messages lazily when needed
          lastActivity: new Date(session.updated_at),
          iconName: session.icon_name
        }
      })

      console.log('🔍 [getChatSessions] Successfully converted all sessions, sorting by last activity...');
      const sortedSessions = convertedSessions.sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());
      console.log('✅ [getChatSessions] Returning', sortedSessions.length, 'chat sessions');
      return sortedSessions;
    } catch (error) {
      console.error('❌ [getChatSessions] Error getting chat sessions:', error)
      console.log('🔍 [getChatSessions] Falling back to JSON data due to error');
      return this.getFallbackChatSessions()
    }
  }

  // Load messages for a specific chat session
  async getMessagesForSession(sessionId: string): Promise<Message[]> {
    try {
      console.log('🔍 [getMessagesForSession] Loading messages for session:', sessionId);
      const { data: messages, error } = await supabaseHelpers.getMessages(sessionId);

      if (error) {
        console.error('❌ [getMessagesForSession] Error fetching messages:', error);
        return [];
      }

      const convertedMessages = (messages || []).map(msg => ({
        id: msg.id,
        text: msg.content,
        sender: msg.sender_type as 'user' | 'ai',
        timestamp: msg.timestamp,
        avatar: msg.avatar_url || undefined,
        chatId: sessionId,
        isLoading: msg.is_loading,
        isError: msg.is_error,
        groundingChunks: msg.grounding_chunks || []
      }));

      console.log('✅ [getMessagesForSession] Loaded', convertedMessages.length, 'messages');
      return convertedMessages;
    } catch (error) {
      console.error('❌ [getMessagesForSession] Error:', error);
      return [];
    }
  }

  // Create new chat session
  async createChatSession(title: string, iconName: string = 'SpeechBubbleIcon'): Promise<ChatSession | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return null

      const { data: newSession, error } = await supabaseHelpers.createChatSession(user.id, title, iconName)
      if (error || !newSession) {
        console.error('Error creating chat session:', error)
        return null
      }

      return {
        id: newSession.id,
        title: newSession.title,
        messages: [],
        lastActivity: new Date(newSession.created_at),
        iconName: newSession.icon_name
      }
    } catch (error) {
      console.error('Error creating chat session:', error)
      return null
    }
  }

  // Add message to chat session
  async addMessage(chatSessionId: string, message: Omit<Message, 'id' | 'timestamp'>): Promise<Message | null> {
    try {
      console.log('🔍 [dataService.addMessage] Adding message to chat:', chatSessionId);
      console.log('🔍 [dataService.addMessage] Message data:', {
        content: message.text.substring(0, 50),
        sender: message.sender,
        chatId: message.chatId
      });

      const { data: newMessage, error } = await supabaseHelpers.addMessage({
        chat_session_id: chatSessionId,
        content: message.text, // Content should already have proper newlines
        sender_type: message.sender,
        metadata: {
          chatId: message.chatId,
          originalId: message.id || `${message.sender}-${Date.now()}`
        },
        is_loading: message.isLoading || false,
        is_error: message.isError || false,
        avatar_url: message.avatar || null,
        grounding_chunks: message.groundingChunks || []
      })

      if (error || !newMessage) {
        console.error('❌ [dataService.addMessage] Error adding message:', error)
        return null
      }

      console.log('✅ [dataService.addMessage] Message added successfully:', newMessage.id);

      return {
        id: newMessage.id,
        text: newMessage.content,
        sender: newMessage.sender_type as 'user' | 'ai',
        timestamp: newMessage.timestamp,
        avatar: newMessage.avatar_url || undefined,
        chatId: chatSessionId,
        isLoading: newMessage.is_loading,
        isError: newMessage.is_error,
        groundingChunks: newMessage.grounding_chunks || []
      }
    } catch (error) {
      console.error('Error adding message:', error)
      return null
    }
  }

  // Fallback methods for JSON data
  private async getFallbackUserProfiles(): Promise<UserProfiles | null> {
    try {
      const response = await fetch('./data/users.json')
      if (!response.ok) return null
      return await response.json()
    } catch (error) {
      console.error('Error fetching fallback user profiles:', error)
      return null
    }
  }

  private async getFallbackChatSessions(): Promise<ChatSession[]> {
    try {
      const response = await fetch('./data/chats.json')
      if (!response.ok) return []
      const rawChatsData = await response.json()
      
      return rawChatsData.map((chat: any) => ({
        ...chat,
        messages: chat.messages || [],
        lastActivity: new Date(chat.lastActivity),
      }))
    } catch (error) {
      console.error('Error fetching fallback chat sessions:', error)
      return []
    }
  }

  // Authentication methods
  async signIn(email: string, password: string) {
    return supabaseHelpers.signIn(email, password)
  }

  async signOut() {
    return supabaseHelpers.signOut()
  }

  // Subscribe to auth changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }

  // Subscribe to real-time chat updates
  subscribeToChatMessages(chatSessionId: string, callback: (payload: any) => void) {
    return supabaseHelpers.subscribeToChatMessages(chatSessionId, callback)
  }

  subscribeToChatSessions(userId: string, callback: (payload: any) => void) {
    return supabaseHelpers.subscribeToChatSessions(userId, callback)
  }
}

// Export singleton instance
export const dataService = DataService.getInstance()
